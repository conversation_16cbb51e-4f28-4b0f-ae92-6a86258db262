import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    // Ambil parameter dari query string
    const { searchParams } = new URL(request.url);
    const name = searchParams.get('name');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Validasi parameter
    if (!name) {
      return NextResponse.json(
        { message: 'Nama terapis harus diisi' },
        { status: 400 }
      );
    }

    // Konversi string tanggal ke objek Date
    let startDateTime = startDate ? new Date(startDate) : new Date();
    let endDateTime = endDate ? new Date(endDate) : new Date();

    // Set waktu untuk tanggal mulai ke 00:00:00
    startDateTime.setHours(0, 0, 0, 0);

    // Set waktu untuk tanggal akhir ke 23:59:59
    endDateTime.setHours(23, 59, 59, 999);

    // Cari terapis berdasarkan nama
    const therapist = await prisma.therapist.findFirst({
      where: {
        name: {
          contains: name,
          mode: 'insensitive',
        },
      }
    });

    if (!therapist) {
      // Coba cari dengan nama yang lebih fleksibel
      const partialNameTherapist = await prisma.therapist.findFirst({
        where: {
          OR: [
            { name: { startsWith: name, mode: 'insensitive' } },
            { name: { endsWith: name, mode: 'insensitive' } },
            { name: { contains: name.split(' ')[0], mode: 'insensitive' } },
          ],
        }
      });

      if (partialNameTherapist) {
        return NextResponse.json({
          message: `Terapis dengan nama persis "${name}" tidak ditemukan, tetapi kami menemukan "${partialNameTherapist.name}". Apakah ini yang Anda maksud?`,
          therapistName: partialNameTherapist.name,
          commissions: [],
          totalSales: 0,
          outlets: [],
          suggestedTherapist: partialNameTherapist.name
        });
      }
    }

    // Jika terapis tidak ditemukan
    if (!therapist) {
      const allTherapists = await prisma.therapist.findMany({
        select: { id: true, name: true, isActive: true }
      });

      return NextResponse.json(
        {
          message: 'Terapis tidak ditemukan',
          allTherapists: allTherapists
        },
        { status: 404 }
      );
    }

    // Ambil data transaksi langsung dari tabel Transaction
    const transactions = await prisma.transaction.findMany({
      where: {
        therapistId: therapist.id,
        transactionDate: {
          gte: startDateTime,
          lte: endDateTime,
        },
        paymentStatus: 'PAID',
      },
      select: {
        id: true,
        displayId: true,
        transactionDate: true,
        therapistCommissionEarned: true,
        totalAmount: true,
        discountAmount: true,
        additionalCharge: true,
        overtimeMinutes: true,
        overtimeAmount: true,
        customer: {
          select: {
            name: true,
          },
        },
        outlet: {
          select: {
            name: true,
          },
        },
        transactionItems: {
          select: {
            id: true,
            price: true,
            quantity: true,
            name: true,
            service: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        transactionDate: 'desc',
      },
    });

    // SIMPLIFIED: Langsung gunakan data dari database tanpa penghitungan ulang
    const commissions = transactions.map(transaction => {
      // Format nama layanan dengan quantity
      const serviceName = transaction.transactionItems.map(item => {
        const name = item.service?.name || item.name || 'Item Kustom';
        return item.quantity > 1 ? `${name} (${item.quantity}x)` : name;
      }).join(', ');

      // Gunakan langsung nilai therapistCommissionEarned dari database
      const commissionValue = transaction.therapistCommissionEarned || 0;

      return {
        transactionId: transaction.displayId || `TR${String(transaction.id).padStart(7, '0')}`,
        transactionDate: transaction.transactionDate,
        serviceName: serviceName,
        customerName: transaction.customer?.name || 'Pelanggan Umum',
        amount: transaction.totalAmount,
        commission: commissionValue,
        outletName: transaction.outlet.name,
        // Tambahkan informasi diskon, biaya tambahan, dan lembur jika ada
        discountAmount: transaction.discountAmount || 0,
        additionalCharge: transaction.additionalCharge || 0,
        overtimeMinutes: transaction.overtimeMinutes || 0,
        overtimeAmount: transaction.overtimeAmount || 0
      };
    });

    // Hitung total omzet layanan (TANPA lembur)
    // Total omzet layanan = totalAmount + additionalCharge (tanpa overtimeAmount karena lembur bukan omzet layanan)
    const totalSales = transactions.reduce((sum, transaction) => {
      // Pastikan semua nilai adalah angka yang valid
      let therapistSales = Number(transaction.totalAmount) || 0;
      
      // Validasi nilai individual untuk mencegah nilai yang tidak wajar
      const maxIndividualValue = 100000000; // 100 juta per transaksi
      if (therapistSales > maxIndividualValue) {
        console.warn(`Nilai totalAmount terlalu besar untuk transaksi ${transaction.id}: ${therapistSales}`);
        therapistSales = maxIndividualValue;
      }
      
      // Jika ada diskon, tambahkan kembali ke total untuk mendapatkan harga asli
      if (transaction.discountAmount && transaction.discountAmount > 0) {
        const discountAmount = Number(transaction.discountAmount) || 0;
        if (discountAmount <= maxIndividualValue) {
          therapistSales += discountAmount;
        }
      }
      
      // Tambahkan biaya tambahan ke omzet layanan
      if (transaction.additionalCharge && transaction.additionalCharge > 0) {
        const additionalCharge = Number(transaction.additionalCharge) || 0;
        if (additionalCharge <= maxIndividualValue) {
          therapistSales += additionalCharge;
        }
      }
      
      // TIDAK menambahkan lembur ke omzet layanan - lembur bukan bagian dari omzet layanan
      
      // Pastikan hasil tidak infinity atau NaN
      const validSum = Number(sum) || 0;
      const validTherapistSales = Number(therapistSales) || 0;
      const result = validSum + validTherapistSales;
      
      // Batasi total keseluruhan juga
      const maxTotalValue = 999999999999; // 999 miliar
      if (result > maxTotalValue) {
        console.warn(`Total sales melebihi batas maksimum: ${result}`);
        return maxTotalValue;
      }
      
      return isFinite(result) ? result : 0;
    }, 0);
    
    const totalCommission = transactions.reduce((sum, transaction) => {
      const validSum = Number(sum) || 0;
      let validCommission = Number(transaction.therapistCommissionEarned) || 0;
      
      // Validasi nilai komisi individual
      const maxCommissionValue = 50000000; // 50 juta per transaksi
      if (validCommission > maxCommissionValue) {
        console.warn(`Nilai komisi terlalu besar untuk transaksi ${transaction.id}: ${validCommission}`);
        validCommission = maxCommissionValue;
      }
      
      const result = validSum + validCommission;
      
      // Batasi total komisi keseluruhan
      const maxTotalCommission = 999999999999; // 999 miliar
      if (result > maxTotalCommission) {
        console.warn(`Total commission melebihi batas maksimum: ${result}`);
        return maxTotalCommission;
      }
      
      return isFinite(result) ? result : 0;
    }, 0);

    // Dapatkan daftar outlet unik
    const uniqueOutlets = [...new Set(transactions.map(transaction => transaction.outlet.name))];

    // Pesan berdasarkan hasil
    const message = commissions.length > 0
      ? 'Data komisi berhasil diambil'
      : `Tidak ada data komisi untuk terapis ${therapist.name} dalam rentang tanggal yang dipilih`;

    return NextResponse.json({
      message,
      therapistName: therapist.name,
      commissions,
      totalSales,
      totalCommission,
      outlets: uniqueOutlets,
      hasData: commissions.length > 0
    });
  } catch (error) {
    console.error('[API GET /api/therapists/commissions] Error:', error);

    return NextResponse.json(
      { message: 'Terjadi kesalahan saat mengambil data komisi' },
      { status: 500 }
    );
  }
}
